// functions/api/create-order.js
// Order creation function for Cloudflare Pages
// Creates orders, calculates totals, stores in KV, and returns structured JSON

import forge from 'node-forge';

export async function onRequestPost(context) {
  const { request, env } = context;

  // CORS headers for better browser compatibility
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Content-Type': 'application/json'
  };

  try {
    // Parse request body to get dishes data
    let requestData;
    try {
      requestData = await request.json();
    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid JSON in request body',
        statusCode: 400
      }), {
        status: 400,
        headers: corsHeaders
      });
    }

    // Validate dishes parameter
    const { dishes } = requestData;
    if (!dishes || !Array.isArray(dishes) || dishes.length === 0) {
      return new Response(JSON.stringify({
        success: false,
        error: 'dishes parameter is required and must be a non-empty array',
        statusCode: 400
      }), {
        status: 400,
        headers: corsHeaders
      });
    }

    // Generate UUID v4 using forge (same pattern as keepz.js)
    function generateUUID() {
      const bytes = forge.random.getBytesSync(16);
      const hex = forge.util.bytesToHex(bytes);
      return [
        hex.substring(0, 8),
        hex.substring(8, 12),
        '4' + hex.substring(13, 16),
        ((parseInt(hex.substring(16, 17), 16) & 0x3) | 0x8).toString(16) + hex.substring(17, 20),
        hex.substring(20, 32)
      ].join('-');
    }

    // Calculate total amount from dishes
    let totalAmount = 0;
    let totalQuantity = 0;
    const processedDishes = [];

    for (const dish of dishes) {
      // Validate dish structure
      if (!dish.id || !dish.price) {
        return new Response(JSON.stringify({
          success: false,
          error: 'Each dish must have id and price properties',
          statusCode: 400
        }), {
          status: 400,
          headers: corsHeaders
        });
      }

      const quantity = dish.quantity || 1;
      const price = parseFloat(dish.price);
      
      if (isNaN(price) || price < 0) {
        return new Response(JSON.stringify({
          success: false,
          error: `Invalid price for dish ${dish.id}: ${dish.price}`,
          statusCode: 400
        }), {
          status: 400,
          headers: corsHeaders
        });
      }

      if (quantity < 1) {
        return new Response(JSON.stringify({
          success: false,
          error: `Invalid quantity for dish ${dish.id}: ${quantity}`,
          statusCode: 400
        }), {
          status: 400,
          headers: corsHeaders
        });
      }

      const dishTotal = price * quantity;
      totalAmount += dishTotal;
      totalQuantity += quantity;

      // Store processed dish with calculated totals
      processedDishes.push({
        ...dish,
        quantity,
        price,
        total: dishTotal
      });
    }

    // Generate unique order ID
    const orderId = generateUUID();
    const kvKey = `orders:${orderId}`;

    // Create order data object
    const orderData = {
      orderId,
      dishes: processedDishes,
      totalAmount: Math.round(totalAmount * 100) / 100, // Round to 2 decimal places
      totalQuantity,
      createdAt: new Date().toISOString(),
      status: 'created'
    };

    // Store order in Cloudflare KV
    try {
      // Check if KV namespace is available
      if (!env.ORDERS_KV) {
        return new Response(JSON.stringify({
          success: false,
          error: 'KV storage not configured. Please set up ORDERS_KV binding.',
          statusCode: 500
        }), {
          status: 500,
          headers: corsHeaders
        });
      }

      await env.ORDERS_KV.put(kvKey, JSON.stringify(orderData));
    } catch (kvError) {
      console.error('KV storage error:', kvError);
      return new Response(JSON.stringify({
        success: false,
        error: 'Failed to store order data',
        statusCode: 500
      }), {
        status: 500,
        headers: corsHeaders
      });
    }

    // Return structured JSON response
    const response = {
      success: true,
      data: {
        orderId,
        totalAmount: orderData.totalAmount,
        totalQuantity: orderData.totalQuantity,
        dishCount: processedDishes.length,
        createdAt: orderData.createdAt,
        status: orderData.status
      },
      message: 'Order created successfully'
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: 201,
      headers: corsHeaders
    });

  } catch (error) {
    console.error('Function error:', error.message);
    return new Response(JSON.stringify({
      success: false,
      error: `Function error: ${error.message}`,
      statusCode: 500
    }), {
      status: 500,
      headers: corsHeaders
    });
  }
}

// Handle OPTIONS requests for CORS
export async function onRequestOptions(context) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    }
  });
}

// Handle other HTTP methods
export async function onRequest(context) {
  const { request } = context;

  if (request.method === 'OPTIONS') {
    return onRequestOptions(context);
  }

  if (request.method !== 'POST') {
    return new Response(JSON.stringify({
      success: false,
      error: 'Method not allowed. Only POST requests are supported.',
      statusCode: 405
    }), {
      status: 405,
      headers: {
        'Allow': 'POST, OPTIONS',
        'Content-Type': 'application/json'
      }
    });
  }

  // This shouldn't be reached due to onRequestPost, but just in case
  return new Response(JSON.stringify({
    success: false,
    error: 'Use POST method',
    statusCode: 405
  }), {
    status: 405,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}
