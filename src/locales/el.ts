export default {
  home: "σπίτι",
  offers: "προσφέρει",
  pages: "σελίδα",
  cart: "Καλάθι",
  new: "νέα",
  goToTheRestaurant: "Πηγαίνετε στο εστιατόριο",
  orderOnline: "παραγγελία-online",
  gallery: "συλλογή",
  restaurantInfo: "πληροφορίες εστιατορίου",
  bookTable: "πίνακας βιβλίων",
  ratingReview: "επανεξέταση",
  dishesSearchTitle: "Αναζήτηση Πιάτων",
  addToCart: "ΠΡΟΣΘΈΣΕΤΕ",
  viewMore: "Δείτε Περισσότερα",
  order: "ΠΑΡΑΓΓΕΛΊΑ",
  subTotal: "Υποσύνολο",
  checkout: "ΤΑΜΕΊΟ",
  yourOrder: "Η Παραγγελία Σας",
  closed: "ΚΛΕΙΣΤΌ",
  open: "ΑΝΟΊΞΕΤΕ",
  waiter: "Σερβιτόρος",
  total: "Συνολική",
  service: "Υπηρεσία",
  mins: "λεπτά",
  tip: "Συμβουλή",
  visitUs: "Επισκεφθείτε μας ξανά!",
  thankYou: "ευχαριστώ",
  yourCheck: "Η Επιταγή Σας",
  table: "Πίνακας",
  space: "χώρος",
  orderDate: "ημερομηνία παραγγελίας",
  giveAdmin: "δώστε διαχειριστή",
  adminRequest: "αίτηση διαχειριστή",
  checkOrder: "έλεγχος παραγγελίας",
  choosePayMethod: "επιλέξτε μέθοδο πληρωμής",
  card: "κάρτα",
  cash: "μετρητά",
  withTheSearchParameter: "με την παράμετρο αναζήτησης",
  NoProductFound: "Δεν βρέθηκε προϊόν",
  chooseAmount: "Επιλέξτε ένα ποσό",
  noOrder: "χωρίς παραγγελία",
  errorOccured: "Παρουσιάστηκε σφάλμα",
  youAreAdmin: "είστε διαχειριστής",
  youAreNotAdmin: "δεν είστε διαχειριστής",
  requestSentSuccessfully: "το αίτημα στάλθηκε με επιτυχία",
  AdminTransferred: "Η κατάσταση διαχειριστή μεταφέρθηκε με επιτυχία",
  AdminNotTransferred:
    "Δεν ήταν δυνατή η ενεργοποίηση της κατάστασης διαχειριστή",
  grantedAdminStatus: "Σας έχει χορηγηθεί το καθεστώς διαχειριστή",
  canNotCallwaiter: "δεν μπορεί να καλέσει έναν σερβιτόρο",
  canNotRate: "δεν μπορεί να βαθμολογήσει",
  canNotUpdateCart: "Δεν είναι δυνατή η ενημέρωση του καλαθιού",
  canNotAddDishInCart: "Συγνώμη δεν μπορείτε να προσθέσετε το πιάτο στο καλάθι",
  NoOrdPayMethod: "Δεν έχει ήδη επιλεγεί μέθοδος παραγγελίας ή πληρωμής",
  canNotUpdateOrder: "δεν είναι δυνατή η ενημέρωση της παραγγελίας",
  canNotaddOrder: "δεν είναι δυνατή η προσθήκη παραγγελίας",
  canNotIncreaseOrder: "δεν μπορεί να αυξήσει τη σειρά",
  calledWaiter: "ονομάζεται σερβιτόρος",
  ka: "Georgian",
  en: "English",
  ru: "Russian",
  el: "Greek",
  someoneAskForAdmin: "κάποιος χρήστης ζητά διαχειριστή",
  cantDisplayDishes: "...ops υπάρχει (σφάλμα) δεν μπορεί να εμφανίσει πιάτα",
  noDishes: "Δεν υπάρχουν πιάτα",
  thanksForOrder: "Ευχαριστώ για την παραγγελία",
  enter: "εισαγάγετε",
  basket: "καλάθι",
  main: "Κύριος",
  shareCheck: "Κοινή χρήση ελέγχου",
  orderCreatedSuccessfully: "Η παραγγελία δημιουργήθηκε με επιτυχία",
  menu: "Menu",
  chooseTip: "Επιλέξτε την ποσότητα του φιλοδωρήματος",
  customerWantsToEdit: "The customer wants to be able to edit the order",
  accept: "Accept",
  deny: "Deny",
  requestReceived: "Request Received",
  receiptCheck: "Receipt Check",
  waitForBillCheck: "Wait for your bill to be checked at the desk",
  guest: "Guest",
  useMenuCantOrder: "You can use the menu, but you can't order",
  continueAsGuest: "Continue as a guest",
  serviceFee: "Κόστος υπηρεσίας",
  previouslyOrdered: "Προηγούμενες παραγγελίες",
  howMuchCashUse: "Πόσα μετρητά θα χρησιμοποιήσετε;",
  notifyTheWaiter: "Θα ενημερώσουμε τον σερβιτόρο να φέρει τα ρέστα.",
  confirm: "Confirm",
  haveExactAmount: "έχω ακριβώς",
  payingWithCash: "Πληρωμή με μετρητά",
  payingWithCard: "Πληρωμή με κάρτα",
  searchDish: "Search for the desired dish",
  noDish: "Δεν βρέθηκαν πιάτα",
  emptyCart: "Το καλάθι είναι άδειο",
  restaurants: "Εστιατόρια",
  howItWorks: "Πως δουλεύει",
  contactUs: "Επικοινωνήστε μαζί μας",
  copyrightInformation: "Copyright Information",
  privacyPolicy: "Πολιτική Απορρήτου",
  termsOfUse: "Οροι χρήσης",
  discoverGreatRestaurants:
    "Ανακαλύψτε υπέροχα εστιατόρια με <span class='font-extrabold'>MenuHub</span>",
  with: "",
  aboutUs: "Σχετικά με εμάς",
  whatsMenuHub: "Τι είναι το MenuHub?",
  welcomeToMNU:
    "Καλώς ήρθατε στο MNU, τον προορισμό σας για να εξερευνήσετε τα καλύτερα εστιατόρια της πόλης. Με το MNU, μπορείτε να ανακαλύψετε έναν κόσμο γαστρονομικών απολαύσεων, να περιηγηθείτε στα μενού και να επιλέξετε τη γευστική σας εμπειρία—όλα σε μία εφαρμογή. Είτε γευματίζετε είτε παίρνετε μαζί σας, σας καλύπτουμε.",
  browseRestaurants:
    "Περιηγηθείτε σε εστιατόρια: Εξερευνήστε μια επιλεγμένη λίστα εστιατορίων και τα λαχταριστά πιάτα τους.",
  customizeYourOrder:
    "Προσαρμογή της παραγγελίας σας: Επιλέξτε τα αγαπημένα σας στοιχεία από το μενού και προσαρμόστε τα σύμφωνα με τις προτιμήσεις σας.",
  chooseYourSetting:
    "Επιλέξτε τη ρύθμισή σας: Επιλέξτε μια άνετη εμπειρία δείπνου ή επιλέξτε να παραλάβετε την παραγγελία σας.",
  exploreMore:
    "Εξερευνήστε περισσότερα: Ελέγξτε τις τοποθεσίες των εστιατορίων, τις ώρες λειτουργίας και μείνετε ενημερωμένοι με εκδηλώσεις και νέα",
  name: "el_name",
  comments: "el_comments",
  email: "el_email",
  submit: "el_submit",
  firstName: "First Name",
  lastName: "Last Name",
  restaurantName: "Restaurant Name",
  password: "Password",
  repeatPassword: "Repeat Password",
  iAgree: "I Agree to MenuHub’s <span class=''>Terms & Conditions</span>",
  nameRequired: "Name is required",
  restaurantNameRequired: "Restaurant name is required",
  invalidEmail: "Please provide correct email",
  invalidPhone: "Please provide correct phone",
  passwordRequired: "Password is required",
  passwordMismatch: "Make sure passwords match",
  agreeRequired: "Please agree to terms and conditions",
  validationFailed: "Validation Failed",
  registrationSuccess: "Registeration Successful",
  badRequest: "Bad Request",
  serverError: "Error occured. Please try again later",
  submissionFailed: "Submission Failed",
  register: "Register",
  totalPaid: "Συνολικά πληρωμένα",
  change: "Αλλαγή",
  incorrectAmount: "Εισαγάγετε το σωστό ποσό!",
  canNotUpdateRatings: "Δεν είναι δυνατή η ενημέρωση των αξιολογήσεων",
  discount: "Εκπτωση",
  apply: "Ισχύουν",
  of: "el_Of",
  discountApplied: "Εφαρμόστηκε έκπτωση!!",
  invalidCode: "Μη έγκυρος κωδικός",
  enterDiscountCode: "Εισαγάγετε τον κωδικό έκπτωσης",
  haveDiscountCode: "εκπτωτικό κωδικό?",
  soon: "Σύντομα",
  awaitingCheckout: "awaitingCheckout",
  profile: "Profile",
  myOrders: "My Orders",
  signUp: "Sign Up",
  account: "Account",
  changePassword: "Change Password",
  logout: "Logout",
  login: "Login",
  currentPassword: "Current Password",
  newPassword: "New Password",
  confirmNewPassword: "Confirm New Password",
  errorFirstNameRequired: "First Name is required",
  errorLastNameRequired: "Last Name is required",
  errorEmailRequired: "Email is required",
  errorInvalidEmail: "Error Invalid email",
  errorPhoneNumberRequired: "Phone Number is required",
  errorInvalidPhoneNumber: "Invalid phone number",
  pleaseEnterValidCredentials: "Please enter valid credentials",
  profileUpdatedSuccessfully: "Profile updated successfully",
  profileUpdateFailed: "Profile Update Failed",
  errorCurrentPasswordRequired: "Current Password is required",
  errorNewPasswordRequired: "New Password is required",
  errorNewPasswordSameAsCurrent:
    "New Password cannot be same as current password",
  errorConfirmNewPasswordRequired: "Confirm New password is required",
  errorNewPasswordMismatch:
    "New Password and Confirm New Password do not match",
  passwordChangedSuccessfully: "Password changed successfully",
  passwordChangeFailed: "Password Change Failed",
  placeAnOrder: "Place an order",
  hello: "Hello",
  orderHistory: "Order History",
  personalInformation: "Personal Information",
  other: "Other",
  view: "View",
  confirmPassword: "Confirm Password",
  thereAreNoOrders: "There are no orders",
  enterData: "Enter your data to enter the system",
  forgotPassword: "Forgot your password?",

  loginWithGoogle: "Login with Google",
  loginWithFacebook: "Login with Facebook",
  errorAgreeToTermsAndConditions: "Please agree to terms and conditions",
  agreeToTermsAndConditions:
    "I have read and agree to the <a class='text-velvet' href='https://menuhub.ge/terms-of-user'>Terms and Conditions</a> and the <a class='text-velvet' href='https://menuhub.ge/privacy'>Privacy Policy</a>",
  errorFirstNameLength: "First Name must be more than 2 characters",
  errorLastNameLength: "Last Name must be more than 2 characters",
  errorPasswordLength: "Password must be at least 8 characters",
  errorPasswordRequired: "Password is required",
  errorConfirmPasswordRequired: "Confirm Password is required",
  errorPasswordMismatch: "Passwords do not match",
  errorNewPasswordLength: "New Password must be at least 8 characters",
  // Payment Modal
  processingPayment: "Επεξεργασία πληρωμής",
  pleaseWait: "Παρακαλώ περιμένετε...",
  paymentReady: "Η πληρωμή είναι έτοιμη",
  orderId: "ID παραγγελίας",
  paymentModalInstructions: "Ολοκληρώστε την πληρωμή χρησιμοποιώντας τη φόρμα παρακάτω ή ανοίξτε σε νέα καρτέλα για καλύτερη εμπειρία.",
  openInNewTab: "Άνοιγμα σε νέα καρτέλα",
  close: "Κλείσιμο",
  paymentModalHelp: "Αντιμετωπίζετε προβλήματα; Δοκιμάστε να ανοίξετε τη σελίδα πληρωμής σε νέα καρτέλα.",
  paymentError: "Σφάλμα πληρωμής",
  paymentErrorMessage: "Δεν ήταν δυνατή η φόρτωση της σελίδας πληρωμής. Παρακαλώ δοκιμάστε ξανά.",
  paymentSuccessful: "Επιτυχής πληρωμή!",
  paymentSuccessMessage: "Η πληρωμή σας επεξεργάστηκε με επιτυχία. Ευχαριστούμε για την παραγγελία σας!",
  paymentFailed: "Η πληρωμή απέτυχε",
  deliveryLocation: "Τοποθεσία παράδοσης",
  contactInformation: "Στοιχεία επικοινωνίας",
  phoneNumber: "Αριθμός τηλεφώνου",
  enterPhoneNumber: "Εισάγετε τον αριθμό τηλεφώνου σας",
  phoneNumberHelp: "Θα χρησιμοποιήσουμε αυτόν τον αριθμό για να επικοινωνήσουμε μαζί σας σχετικά με την παραγγελία σας",
};
