<script setup>
import { watch, onMounted, onUnmounted } from 'vue';

defineEmits(["clickOut"]);
const props = defineProps({
  modalActive: {
    type: Boolean,
    default: false,
  },
});

// Prevent body scrolling when modal is open
const toggleBodyScroll = (disable) => {
  if (process.client) {
    if (disable) {
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.width = '100%';
    } else {
      document.body.style.overflow = '';
      document.body.style.position = '';
      document.body.style.width = '';
    }
  }
};

// Watch for modal state changes
watch(() => props.modalActive, (isActive) => {
  toggleBodyScroll(isActive);
});

// Cleanup on unmount
onUnmounted(() => {
  toggleBodyScroll(false);
});
</script>

<template>
  <Teleport to="body">
    <Transition name="modal-outer">
      <div
        v-show="modalActive"
        @click="$emit('clickOut')"
        class="fixed inset-0 z-50 bg-black bg-opacity-30 flex items-start justify-center p-4 overflow-y-auto"
      >
        <Transition name="modal-inner">
          <div
            v-if="modalActive"
            v-bind="$attrs"
            @click.stop
            class="bg-white rounded-3xl w-full max-w-screen-md my-8 relative"
            style="min-height: fit-content;"
          >
            <div class="p-4 py-6">
              <slot />
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<style scoped>
/* Modal overlay with proper scrolling */
.fixed.inset-0 {
  /* Ensure the modal overlay covers the entire viewport */
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  /* Enable scrolling when content overflows */
  overflow-y: auto;
  /* Prevent background scrolling on mobile */
  overscroll-behavior: contain;
  /* Smooth scrolling on iOS */
  -webkit-overflow-scrolling: touch;
}

/* Modal content container */
.bg-white.rounded-3xl {
  /* Ensure modal content can grow as needed */
  max-height: calc(100vh - 4rem);
  /* Add some breathing room on mobile */
  margin-top: 2rem;
  margin-bottom: 2rem;
}

/* Mobile-specific adjustments */
@media (max-width: 640px) {
  .fixed.inset-0 {
    padding: 1rem;
  }

  .bg-white.rounded-3xl {
    margin-top: 1rem;
    margin-bottom: 1rem;
    max-height: calc(100vh - 2rem);
  }
}

/* Transition animations */
.modal-outer-enter-active,
.modal-outer-leave-active {
  transition: opacity 0.3s cubic-bezier(0.52, 0.02, 0.19, 1.02);
}

.modal-outer-enter-from,
.modal-outer-leave-to {
  opacity: 0;
}

.modal-inner-enter-active {
  transition: all 0.3s cubic-bezier(0.52, 0.02, 0.19, 1.02) 0.15s;
}

.modal-inner-leave-active {
  transition: all 0.3s cubic-bezier(0.52, 0.02, 0.19, 1.02);
}

.modal-inner-enter-from {
  opacity: 0;
  transform: scale(0.8);
}

.modal-inner-leave-to {
  transform: scale(0.8);
}
</style>
