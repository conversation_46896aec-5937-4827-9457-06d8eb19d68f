<script setup lang="ts">
import BaseModal from "./BaseModal.client.vue";
import Button from "./Button.vue";
import { ref, onMounted, onUnmounted, watch } from 'vue';

interface Props {
  isShowModal: boolean;
  paymentUrl?: string;
  orderId?: string;
  amount?: number;
  isLoading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isShowModal: false,
  isLoading: false
});

const emit = defineEmits<{
  clickOut: [];
  close: [];
  openInNewTab: [];
  paymentSuccess: [orderId: string, amount: number];
  paymentError: [error: string];
}>();

const { t } = useI18n();
const { $successToast, $warningToast } = useNuxtApp();

// Payment completion detection
const paymentCompleted = ref(false);
const paymentError = ref('');
const iframeRef = ref<HTMLIFrameElement | null>(null);

// Listen for payment completion messages from iframe
const handleMessage = (event: MessageEvent) => {
  // Only accept messages from the payment domain
  if (!props.paymentUrl || !event.origin.includes('keepz.me')) {
    return;
  }

  try {
    const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;

    if (data.type === 'payment_success' || data.status === 'success' || data.success === true) {
      handlePaymentSuccess();
    } else if (data.type === 'payment_error' || data.status === 'error' || data.error) {
      handlePaymentError(data.message || data.error || 'Payment failed');
    }
  } catch (error) {
    // Ignore invalid messages
    console.log('Received non-JSON message from iframe:', event.data);
  }
};

const handlePaymentSuccess = () => {
  paymentCompleted.value = true;

  // Show success notification
  $successToast(t('paymentSuccessful'));

  // Emit payment success event
  emit('paymentSuccess', props.orderId || '', props.amount || 0);

  // Close modal after a short delay to show the success state
  setTimeout(() => {
    closeModal();
  }, 2000);
};

const handlePaymentError = (errorMessage: string) => {
  paymentError.value = errorMessage;
  $warningToast(t('paymentFailed'));
  emit('paymentError', errorMessage);
};

const openInNewTab = () => {
  if (props.paymentUrl) {
    window.open(props.paymentUrl, '_blank');
    emit('openInNewTab');
  }
};

const closeModal = () => {
  paymentCompleted.value = false;
  paymentError.value = '';
  emit('close');
};

// Set up message listener when modal opens
watch(() => props.isShowModal, (isOpen) => {
  if (isOpen) {
    window.addEventListener('message', handleMessage);
  } else {
    window.removeEventListener('message', handleMessage);
    paymentCompleted.value = false;
    paymentError.value = '';
  }
});

onMounted(() => {
  if (props.isShowModal) {
    window.addEventListener('message', handleMessage);
  }
});

onUnmounted(() => {
  window.removeEventListener('message', handleMessage);
});
</script>

<template>
  <BaseModal :modalActive="isShowModal" @clickOut="$emit('clickOut')">
    <!-- Responsive container with proper sizing -->
    <main class="w-full max-w-4xl mx-auto px-2 sm:px-4 py-2 sm:py-4 flex flex-col gap-3 sm:gap-4 font-semibold text-cocoa h-full">

      <!-- Payment Success State -->
      <div v-if="paymentCompleted" class="flex flex-col items-center gap-4 text-center">
        <div class="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center animate-bounce">
          <Icon name="material-symbols:check-circle" class="text-3xl text-white" />
        </div>
        <h3 class="text-2xl font-semibold text-green-600">{{ t('paymentSuccessful') }}</h3>
        <p class="text-sm text-gray-600">{{ t('paymentSuccessMessage') }}</p>
        <div v-if="orderId || amount" class="bg-green-50 border border-green-200 rounded-lg p-4 w-full max-w-md">
          <p v-if="orderId" class="text-sm text-green-700 mb-2">
            <span class="font-medium">{{ t('orderId') }}:</span>
            <span class="font-mono text-xs">{{ orderId.substring(0, 8) }}...</span>
          </p>
          <p v-if="amount" class="text-lg font-bold text-green-600">
            {{ amount.toFixed(2) }} ₾
          </p>
        </div>
      </div>

      <!-- Loading State -->
      <div v-else-if="isLoading" class="flex flex-col items-center gap-4 text-center">
        <Icon name="svg-spinners:180-ring" class="text-4xl text-velvet" />
        <h3 class="text-xl font-semibold">{{ t('processingPayment') }}</h3>
        <p class="text-sm text-gray-600">{{ t('pleaseWait') }}</p>
      </div>

      <!-- Payment Ready State -->
      <div v-else-if="paymentUrl" class="flex flex-col lg:flex-row gap-4 lg:gap-6 flex-1 min-h-0">
        <!-- Left side: Order info and controls (mobile: top, desktop: left) -->
        <div class="lg:w-1/3 flex flex-col gap-4 flex-shrink-0">
          <!-- Payment Icon -->
          <div class="flex justify-center lg:justify-start">
            <div class="w-16 h-16 bg-velvet rounded-full flex items-center justify-center">
              <Icon name="tabler:credit-card" class="text-2xl text-white" />
            </div>
          </div>

          <!-- Title -->
          <h3 class="text-xl font-semibold text-center lg:text-left">{{ t('paymentReady') }}</h3>

          <!-- Order Details -->
          <div v-if="orderId || amount" class="bg-gray-50 rounded-lg p-4">
            <p v-if="orderId" class="text-sm text-gray-600 mb-2">
              <span class="font-medium">{{ t('orderId') }}:</span>
              <span class="font-mono text-xs">{{ orderId.substring(0, 8) }}...</span>
            </p>
            <p v-if="amount" class="text-lg font-bold text-velvet">
              {{ amount.toFixed(2) }} ₾
            </p>
          </div>

          <!-- Instructions -->
          <p class="text-sm text-gray-600 leading-relaxed text-center lg:text-left">
            {{ t('paymentModalInstructions') }}
          </p>

          <!-- Action Buttons -->
          <div class="flex flex-col gap-3">
            <!-- Open in New Tab Button -->
            <Button
              @click="openInNewTab"
              class="w-full py-3 bg-velvet hover:bg-velvet/90 transition-colors"
            >
              <div class="flex items-center justify-center gap-2">
                <Icon name="material-symbols:open-in-new" class="text-lg" />
                <span class="font-semibold">{{ t('openInNewTab') }}</span>
              </div>
            </Button>

            <!-- Close Button -->
            <Button
              @click="closeModal"
              class="w-full py-3 bg-velvet hover:bg-velvet/90 transition-colors"
            >
              <span class="font-semibold">{{ t('close') }}</span>
            </Button>
          </div>

          <!-- Help Text -->
          <p class="text-xs text-gray-500 text-center lg:text-left">
            {{ t('paymentModalHelp') }}
          </p>
        </div>

        <!-- Right side: Payment iframe (mobile: bottom, desktop: right) -->
        <div class="lg:w-2/3 flex flex-col min-h-0">
          <!-- Responsive iframe container -->
          <div class="w-full bg-white border border-gray-200 rounded-lg payment-iframe-container">
            <iframe
              ref="iframeRef"
              :src="paymentUrl"
              class="w-full h-full border-0 rounded-lg"
              sandbox="allow-same-origin allow-scripts allow-forms allow-top-navigation allow-popups allow-popups-to-escape-sandbox allow-modals"
              loading="lazy"
              title="Payment Form"
              scrolling="auto"
              frameborder="0"
            ></iframe>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else class="flex flex-col items-center gap-4 text-center max-w-md mx-auto">
        <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center">
          <Icon name="material-symbols:error" class="text-2xl text-white" />
        </div>
        <h3 class="text-xl font-semibold text-red-600">{{ t('paymentError') }}</h3>
        <p class="text-sm text-gray-600">{{ t('paymentErrorMessage') }}</p>
        <p v-if="paymentError" class="text-sm text-red-600 bg-red-50 border border-red-200 rounded-lg p-3">
          {{ paymentError }}
        </p>
        <Button
          @click="closeModal"
          class="w-full py-3 bg-red-500 hover:bg-red-600 transition-colors"
        >
          <span class="font-semibold text-white">{{ t('close') }}</span>
        </Button>
      </div>
    </main>
  </BaseModal>
</template>

<style scoped>
/* Responsive iframe container - mobile first approach */
.payment-iframe-container {
  /* Mobile: Use most of available viewport height */
  height: calc(100vh - 250px);
  min-height: 400px;
  max-height: 80vh;
  /* Remove overflow hidden to allow iframe scrolling */
  overflow: visible;
  /* Ensure container can grow */
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Small mobile devices */
@media (max-width: 480px) {
  .payment-iframe-container {
    height: calc(100vh - 200px);
    min-height: 350px;
    max-height: 85vh;
  }
}

/* Tablet: more space available */
@media (min-width: 641px) and (max-width: 1023px) {
  .payment-iframe-container {
    height: calc(100vh - 300px);
    min-height: 450px;
    max-height: 75vh;
  }
}

/* Desktop: larger iframe with more space */
@media (min-width: 1024px) {
  .payment-iframe-container {
    height: calc(100vh - 350px);
    min-height: 500px;
    max-height: 70vh;
  }
}

/* Large desktop: maximum space utilization */
@media (min-width: 1280px) {
  .payment-iframe-container {
    height: calc(100vh - 400px);
    min-height: 600px;
    max-height: 65vh;
  }
}

/* Ensure iframe is responsive and scrollable */
iframe {
  border: none;
  width: 100%;
  height: 100%;
  border-radius: 0.5rem;
  /* Critical: Allow iframe content to scroll */
  overflow: auto !important;
  /* Smooth scrolling on mobile devices */
  -webkit-overflow-scrolling: touch;
  /* Ensure iframe takes full container space */
  flex: 1;
  /* Minimum height to ensure content is visible */
  min-height: 350px;
}

/* Mobile-specific iframe optimizations */
@media (max-width: 640px) {
  iframe {
    /* Ensure minimum height on mobile */
    min-height: 400px;
    /* Allow content to be scrollable */
    overflow-y: auto !important;
    overflow-x: hidden;
  }

  /* Ensure the main container allows scrolling */
  main {
    overflow: visible;
    max-height: none;
  }
}

/* Success animation */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.animate-bounce {
  animation: bounce 1s ease-in-out;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive modal adjustments */
@media (max-width: 640px) {
  main {
    padding: 0.5rem;
    gap: 0.75rem;
    height: 100%;
    /* Ensure main container uses full available height */
    min-height: calc(100vh - 1rem);
  }
}

/* Tablet adjustments */
@media (min-width: 641px) and (max-width: 1023px) {
  main {
    padding: 1rem;
    gap: 1rem;
  }
}

/* Desktop adjustments */
@media (min-width: 1024px) {
  main {
    padding: 1.5rem;
    gap: 1.5rem;
  }
}
</style>
