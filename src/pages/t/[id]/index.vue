<script setup lang="ts">
import { Carousel } from "flowbite-vue";

// Add runtime config
const config = useRuntimeConfig();
const isDev = process.env.NODE_ENV !== 'production';

// Composables must be at the top
const { params } = useRoute();
const { searchedItems, items, gallery, getTable, table, restaurant } = useTable();
const { sortedCategories } = useTable();
const { getOrders } = useOrder();
const locale = useLocale();
const loading = useLoading();
const { injectTableLayoutState } = useTableLayoutState();
const isInitialized = injectTableLayoutState();
const { cartDishes, cartDishesPrice, clearCart, getCart } = useCart();
const { orderSubscribe, cartSubscribe, checkoutSubscribe, channel } = usePusher();

// Refs
const searchValue = ref("");
const promoItems = ref([]);
const pusherDebug = ref({
  isSubscribed: false,
  lastOrderUpdate: null,
  lastCartUpdate: null,
  channelName: null
});

// Methods
const goToMenu = async (category) => {
  loading.value = false;
  await navigateTo({
    path: `/t/${params.id}/menu`,
    query: {
      category,
    },
  });
};

// Update handlers
const handleOrderUpdate = async (data) => {
  console.log('New order event received:', data);
  // Don't automatically clear cart on order updates
  // Cart should only be cleared when payment is confirmed successful
  await getOrders();
};

const handleCartUpdate = async (data) => {
  console.log('Cart update event received:', data);
  await getCart();
};

const handleCheckoutUpdate = async (data) => {
  console.log('Checkout event received:', data);
  await getOrders();
};

// Lifecycle hooks
onMounted(async () => {
  if (!table.value) {
    await getTable(params.id);
  }

  // Setup Pusher subscriptions
  if (process.client) {
    console.log('Setting up Pusher subscriptions...');

    try {
      const { orderSubscribe, cartSubscribe, checkoutSubscribe, channel } = await usePusher();

      if (!channel)
        return;

      await orderSubscribe(handleOrderUpdate);
      await cartSubscribe(handleCartUpdate);
      await checkoutSubscribe(handleCheckoutUpdate);

    } catch (error) {
      console.log( error );
    }
  }

  loading.value = false;
});

onBeforeMount(() => {
  loading.value = true;
  if (items.value) {
    items.value.forEach((dish) => {
      if (dish.promos && dish.promos.length > 0) {
        promoItems.value.push(dish);
      }
    });
  }
});

</script>
<template>
  <NuxtLayout name="table-layout">
    <template #headerImage>
      <Icon name="cil:restaurant" size="40" class="text-velvet" />
    </template>
    <template #headerTitle>
      <span>Welcome to</span>
      <br />
      <span class="font-semibold">MenuHub</span>
    </template>
    <main>
      <GalleryCarousel :items="gallery" v-if="gallery && gallery.length > 0" />
      <DishSearch v-model="searchValue" />
      <PromoCarousel :items="promoItems" v-if="promoItems.length > 0" />

      <div class="my-3 mt-6 grid grid-cols-2 gap-3" v-if="!searchValue">
        <CategoryCard
          v-for="category in sortedCategories"
          :key="category.data.id"
          :title="category.name"
          :image="category.data.image"
          @click.prevent="goToMenu(category.data.id)"
        />
      </div>
      <LineSeparator />
      <ItemCards :items="searchedItems(searchValue)" />
    </main>
  </NuxtLayout>
</template>
