<script setup lang="ts">
import { Alert } from "flowbite-vue";
import { ref, computed, watch } from 'vue';

const { restaurant } = useTable();
const { orderedDishesPrice, getOrders } = useOrder();
const { cartDishes, cartDishesPrice, clearCart } = useCart();
const { $warningToast } = useNuxtApp();
const ordered = ref(false);
const { capitalizeFirstLetter } = useHelpers();
const userLocation = ref(null);
const isLocationRequired = ref(true);
const clientPhone = ref('');
const phoneError = ref('');
const isDishesExpanded = ref(false);

const handleLocationSelected = (location: any) => {
  userLocation.value = location;
};

// Function to strip all special characters and spaces from phone number
const stripPhoneNumber = (phone: string): string => {
  return phone.replace(/[^\d]/g, '');
};

// Function to validate phone number
const validatePhoneNumber = (phone: string): boolean => {
  const strippedPhone = stripPhoneNumber(phone);
  // Check if phone number has at least 9 digits (minimum for most countries)
  return strippedPhone.length >= 9;
};

// Function to toggle dishes visibility
const toggleDishesExpanded = () => {
  isDishesExpanded.value = !isDishesExpanded.value;
};

// Computed property for cart summary
const cartSummary = computed(() => {
  if (!cartDishes.value || cartDishes.value.length === 0) {
    return { totalItems: 0, totalQuantity: 0, totalAmount: 0 };
  }

  const totalQuantity = cartDishes.value.reduce((sum, dish) => sum + (dish.quantity || 0), 0);

  // Calculate total amount considering promos
  const totalAmount = cartDishes.value.reduce((sum, dish) => {
    // Check if there is a promo and if newPrice is set in the first promo
    const effectivePrice =
      dish.promos &&
      dish.promos.length > 0 &&
      dish.promos[0] &&
      "newPrice" in dish.promos[0]
        ? dish.promos[0].newPrice
        : dish.price;

    return sum + (effectivePrice as number) * (dish.quantity ?? 0);
  }, 0);

  return {
    totalItems: cartDishes.value.length,
    totalQuantity,
    totalAmount: totalAmount.toFixed(2)
  };
});

const isKeepzLoading = ref(false);

// Payment modal state
const showPaymentModal = ref(false);
const paymentUrl = ref('');
const currentOrderId = ref('');
const currentOrderAmount = ref(0);

const placeOrderAndClearCart = async () => {
  // Reset phone error
  phoneError.value = '';

  // Validate phone number first
  if (!clientPhone.value.trim()) {
    phoneError.value = 'Phone number is required';
    $warningToast('Please enter your phone number');
    return;
  }

  if (!validatePhoneNumber(clientPhone.value)) {
    phoneError.value = 'Please enter a valid phone number (minimum 9 digits)';
    $warningToast('Please enter a valid phone number');
    return;
  }

  if (isLocationRequired.value && !userLocation.value) {
    $warningToast('Please share your location for delivery');
    return;
  }

  // Start order creation and payment process
  isKeepzLoading.value = true;

  try {
    // TODO: Calculate the total amount when API is ready
    // const totalAmount = PaymentSummaries.value.find(summary => summary.title === 'total')?.price || 0;

    // Get stripped phone number for order creation
    const strippedPhone = stripPhoneNumber(clientPhone.value);
    console.log('Order creation with phone:', strippedPhone);

    // TODO: Prepare order data with dishes from cart when API is ready
    // const requestData = {
    //   dishes: cartDishes.value?.map(dish => ({
    //     id: dish.id,
    //     name: dish.title,
    //     price: dish.price,
    //     quantity: dish.quantity
    //   })) || [],
    //   amount: totalAmount,
    //   tableId: null, // Add table ID if available
    //   clientPhone: strippedPhone, // Use stripped phone number
    //   clientId: null  // Add client ID if available
    // };

    // TODO: Send request to create-order API when ready
    // const response = await fetch('/api/create-order', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json'
    //   },
    //   body: JSON.stringify(requestData)
    // });

    // if (!response.ok) {
    //   throw new Error(`HTTP error! status: ${response.status}`);
    // }

    // const orderResult = await response.json();

    // Mock response for testing - replace with real API call
    const orderResult = {
      success: true,
      urlForQR: 'https://tiny.keepz.me/4742hxrs',
      orderId: '1982',
      amount: '11.50'
    };

    if (orderResult.success && orderResult.urlForQR) {
      // Set payment modal data
      paymentUrl.value = orderResult.urlForQR;
      currentOrderId.value = orderResult.orderId;
      currentOrderAmount.value = parseFloat(orderResult.amount) || 0;

      // Show payment modal instead of redirecting
      showPaymentModal.value = true;

      // Don't clear cart here - only clear when payment is successful

    } else {
      throw new Error('Invalid response from order creation API');
    }

  } catch (error) {
    console.error('Order creation error:', error);
    $warningToast('Order creation failed. Please try again.');
  } finally {
    isKeepzLoading.value = false;
  }
};

// Payment modal handlers
const closePaymentModal = () => {
  showPaymentModal.value = false;
  paymentUrl.value = '';
  currentOrderId.value = '';
  currentOrderAmount.value = 0;
};

const handlePaymentModalClose = () => {
  closePaymentModal();
  // Don't mark as ordered when modal is closed - only when payment is successful
  // User can still see their cart and retry payment if needed
};

const handlePaymentSuccess = async (orderId: string, amount: number) => {
  console.log('Payment successful for order:', orderId, 'Amount:', amount);

  // Show success notification (already handled by the modal)
  // Additional logic can be added here, such as:
  // - Updating order status in local state
  // - Redirecting to order confirmation page
  // - Sending analytics events

  // Clear cart only when payment is successful
  await clearCart();

  // Close modal and show order success
  closePaymentModal();
  ordered.value = true;

  // Refresh orders if needed
  getOrders();
};

const handlePaymentError = (error: string) => {
  console.error('Payment error:', error);

  // Additional error handling can be added here
  // The modal already shows the error notification

  // Keep modal open so user can retry or close manually
};

const feeAmont = computed((): number => {
  if (restaurant.value?.fee) {
    return Number((restaurant.value?.fee / 100) * cartDishesPrice.value);
  } else {
    return 0;
  }
});

const calculatePaymentSummaries = () => {
  const cartTotal = cartDishes.value
    ? cartDishes.value.reduce((sum, dish) => {
        // Check if there is a promo and if newPrice is set in the first promo
        const effectivePrice =
          dish.promos &&
          dish.promos.length > 0 &&
          dish.promos[0] &&
          "newPrice" in dish.promos[0]
            ? dish.promos[0].newPrice
            : dish.price;

        return sum + (effectivePrice as number) * (dish.quantity ?? 0);
      }, 0)
    : 0;

  const orderedTotal =
    orderedDishesPrice.value +
    (restaurant.value?.fee
      ? Number((restaurant.value?.fee / 100) * orderedDishesPrice.value)
      : 0);
  const fee = restaurant.value?.fee
    ? Number((restaurant.value?.fee / 100) * cartTotal)
    : 0;
  const grandTotal = cartTotal + orderedTotal + fee;

  return [
    {
      title: "subTotal",
      price: cartTotal.toFixed(2),
    },
    {
      title: "previouslyOrdered",
      price: orderedTotal.toFixed(2),
    },
    {
      title: "serviceFee",
      price: fee.toFixed(2),
    },
    {
      title: "total",
      price: grandTotal.toFixed(2),
    },
  ];
};

const PaymentSummaries = ref(calculatePaymentSummaries());

watch([cartDishes, orderedDishesPrice, feeAmont], () => {
  PaymentSummaries.value = calculatePaymentSummaries();
});
const loading = useLoading();

onMounted(() => {
  loading.value = false;
});
</script>

<template>
  <NuxtLayout name="table-layout">
    <template #headerTitle>
      {{ capitalizeFirstLetter($t("basket")) }}
    </template>
    <main>
      <!-- Collapsible Dishes Section -->
      <section v-if="!ordered && cartDishes && cartDishes.length > 0" class="mb-6">
        <!-- Collapsible Header -->
        <div
          @click="toggleDishesExpanded"
          class="p-3 sm:p-4 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors border border-gray-200 collapsible-header"
        >
          <!-- Mobile Layout: Stacked -->
          <div class="block sm:hidden">
            <!-- Top row: Title and expand/collapse -->
            <div class="flex items-center justify-between mb-2">
              <h3 class="text-base font-semibold text-cocoa">{{ $t('yourOrderItems') }}</h3>
              <div class="flex items-center gap-1">
                <span class="text-xs text-gray-600">
                  {{ isDishesExpanded ? $t('collapse') : $t('expand') }}
                </span>
                <Icon
                  :name="isDishesExpanded ? 'tabler:chevron-up' : 'tabler:chevron-down'"
                  class="text-lg text-gray-600 transition-transform duration-200 chevron-icon"
                  :class="{ 'expanded': isDishesExpanded }"
                />
              </div>
            </div>
            <!-- Bottom row: Summary info -->
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <span class="bg-velvet text-white text-xs px-2 py-1 rounded-full">
                  {{ cartSummary.totalItems }}
                </span>
                <span v-if="cartSummary.totalQuantity > cartSummary.totalItems" class="text-xs text-gray-600">
                  ({{ cartSummary.totalQuantity }} {{ $t('items') }})
                </span>
              </div>
              <span class="text-base font-bold text-velvet">
                {{ cartSummary.totalAmount }} ₾
              </span>
            </div>
          </div>

          <!-- Desktop Layout: Single Row -->
          <div class="hidden sm:flex items-center justify-between">
            <div class="flex items-center gap-3">
              <h3 class="text-lg font-semibold text-cocoa">{{ $t('yourOrderItems') }}</h3>
              <div class="flex items-center gap-2">
                <span class="bg-velvet text-white text-sm px-2 py-1 rounded-full">
                  {{ cartSummary.totalItems }}
                </span>
                <span v-if="cartSummary.totalQuantity > cartSummary.totalItems" class="text-sm text-gray-600">
                  ({{ cartSummary.totalQuantity }} {{ $t('items') }})
                </span>
                <span class="text-lg font-bold text-velvet">
                  {{ cartSummary.totalAmount }} ₾
                </span>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-sm text-gray-600">
                {{ isDishesExpanded ? $t('collapse') : $t('expand') }}
              </span>
              <Icon
                :name="isDishesExpanded ? 'tabler:chevron-up' : 'tabler:chevron-down'"
                class="text-xl text-gray-600 transition-transform duration-200 chevron-icon"
                :class="{ 'expanded': isDishesExpanded }"
              />
            </div>
          </div>
        </div>

        <!-- Collapsible Content -->
        <Transition
          name="collapse"
          enter-active-class="transition-all duration-300 ease-out"
          leave-active-class="transition-all duration-300 ease-in"
          enter-from-class="opacity-0 max-h-0"
          enter-to-class="opacity-100 max-h-screen"
          leave-from-class="opacity-100 max-h-screen"
          leave-to-class="opacity-0 max-h-0"
        >
          <div v-show="isDishesExpanded" class="overflow-hidden">
            <div class="collapsible-content rounded-b-lg">
              <div class="p-4">
                <div class="dishes-container">
                  <ItemCards
                    :items="cartDishes?.map((dish, index) => ({ ...dish, sound: '', rowIndex: index })) ?? []"
                    :alert="$t('noDishes')"
                    :showImage="false"
                    class="alternating-rows"
                  />
                </div>
              </div>
            </div>
          </div>
        </Transition>
      </section>

      <!-- Empty Cart Message -->
      <section v-if="!ordered && (!cartDishes || cartDishes.length === 0)">
        <div class="text-center py-8">
          <Icon name="tabler:shopping-cart-off" class="text-6xl text-gray-400 mx-auto mb-4" />
          <p class="text-gray-600 text-lg">{{ $t('emptyCart') }}</p>
        </div>
      </section>

      <!-- Phone Number Input -->
      <section class="mt-6" v-if="cartDishesPrice && !ordered">
        <h3 class="text-lg font-semibold mb-4">{{ $t('contactInformation') }}</h3>
        <div class="space-y-2">
          <label for="clientPhone" class="block text-sm font-medium text-gray-700">
            {{ $t('phoneNumber') }} <span class="text-red-500">*</span>
          </label>
          <input
            id="clientPhone"
            v-model="clientPhone"
            type="tel"
            :placeholder="$t('enterPhoneNumber')"
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-velvet focus:border-velvet transition-colors"
            :class="{ 'border-red-500 focus:ring-red-500 focus:border-red-500': phoneError }"
          />
          <p v-if="phoneError" class="text-sm text-red-600">{{ phoneError }}</p>
          <p class="text-xs text-gray-500">{{ $t('phoneNumberHelp') }}</p>
        </div>
      </section>

      <!-- Add Location Picker -->
      <section class="mt-6" v-if="cartDishesPrice && !ordered">
        <h3 class="text-lg font-semibold mb-4">{{ $t('deliveryLocation') }}</h3>
        <LocationPicker @location-selected="handleLocationSelected" />
      </section>

      <section class="mt-6" v-if="cartDishesPrice">
        <ul>
          <PaymentSummary
            v-for="(summary, index) in PaymentSummaries"
            :key="index"
            :title="summary.title"
            :price="summary.price"
          />
        </ul>
        <Button
          @click="placeOrderAndClearCart"
          :disabled="isKeepzLoading"
          :class="[
            'w-full mt-4 py-4 font-bold flex justify-center items-center',
            isKeepzLoading ? 'bg-gray-400 cursor-not-allowed' : 'bg-velvet'
          ]"
        >
          <div v-if="isKeepzLoading" class="flex items-center">
            <Icon name="svg-spinners:180-ring" class="mr-2" />
            <span class="font-semibold tracking-widest">Processing...</span>
          </div>
          <span v-else class="font-semibold tracking-widest">
            {{ $t("order") }}
          </span>
        </Button>
      </section>
      <section v-if="ordered">
        <Alert type="success">{{ $t("orderCreatedSuccessfully") }}</Alert>
      </section>
    </main>

    <!-- Payment Modal -->
    <PaymentModal
      :isShowModal="showPaymentModal"
      :paymentUrl="paymentUrl"
      :orderId="currentOrderId"
      :amount="currentOrderAmount"
      :isLoading="isKeepzLoading"
      @clickOut="closePaymentModal"
      @close="handlePaymentModalClose"
      @openInNewTab="closePaymentModal"
      @paymentSuccess="handlePaymentSuccess"
      @paymentError="handlePaymentError"
    />
  </NuxtLayout>
</template>

<style scoped>
/* Collapse animation styles */
.collapse-enter-active,
.collapse-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.collapse-enter-from,
.collapse-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.collapse-enter-to,
.collapse-leave-from {
  opacity: 1;
  max-height: 1000px;
  transform: translateY(0);
}

/* Collapsible content border styling */
.collapsible-content {
  border: 2px solid #e5e7eb;
  border-top: 1px solid #d1d5db;
  background: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Smooth hover effects */
.collapsible-header {
  transition: all 0.2s ease;
}

.collapsible-header:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Mobile-specific adjustments */
@media (max-width: 640px) {
  .collapsible-header {
    /* Reduce hover effects on mobile for better touch experience */
    transform: none;
  }

  .collapsible-header:hover {
    transform: none;
    box-shadow: none;
  }

  /* Ensure text doesn't wrap awkwardly */
  .collapsible-header h3 {
    font-size: 1rem;
    line-height: 1.25;
  }

  /* Compact spacing for mobile */
  .collapsible-header .text-xs {
    font-size: 0.75rem;
  }
}

/* Icon rotation animation */
.chevron-icon {
  transition: transform 0.2s ease;
}

.expanded .chevron-icon {
  transform: rotate(180deg);
}

/* Dishes container styling */
.dishes-container {
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Alternating row backgrounds */
.dishes-container :deep(.grid:nth-child(even)) {
  background-color: #f9fafb; /* Light gray background for even rows */
}

.dishes-container :deep(.grid:nth-child(odd)) {
  background-color: #ffffff; /* White background for odd rows */
}

/* Add subtle padding and smooth transitions for rows */
.dishes-container :deep(.grid) {
  padding: 0.75rem;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f3f4f6;
}

/* Remove border from last item */
.dishes-container :deep(.grid:last-child) {
  border-bottom: none;
}

/* Hover effect for rows */
.dishes-container :deep(.grid:hover) {
  background-color: #f3f4f6 !important;
}
</style>
